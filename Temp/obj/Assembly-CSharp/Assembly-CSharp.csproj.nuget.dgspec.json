{"format": 1, "restore": {"/Users/<USER>/Desktop/Projects/mup.game.match3/Assembly-CSharp.csproj": {}}, "projects": {"/Users/<USER>/Desktop/Projects/mup.game.match3/Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/Projects/mup.game.match3/Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "/Users/<USER>/Desktop/Projects/mup.game.match3/Assembly-CSharp.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/Projects/mup.game.match3/Temp/obj/Assembly-CSharp/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.101/RuntimeIdentifierGraph.json"}}}}}