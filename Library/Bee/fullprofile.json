{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 75155, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 75155, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 75155, "tid": 691, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 75155, "tid": 691, "ts": 1750860872740880, "dur": 136, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872741110, "dur": 8, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 75155, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872038997, "dur": 223, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872039221, "dur": 700893, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872039245, "dur": 79, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872039353, "dur": 161421, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872200787, "dur": 42, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872200832, "dur": 1156, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202014, "dur": 3, "ph": "X", "name": "ProcessMessages 3750", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202018, "dur": 73, "ph": "X", "name": "ReadAsync 3750", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202118, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202120, "dur": 58, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202218, "dur": 37, "ph": "X", "name": "ProcessMessages 2057", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202256, "dur": 59, "ph": "X", "name": "ReadAsync 2057", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202322, "dur": 1, "ph": "X", "name": "ProcessMessages 2717", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202324, "dur": 79, "ph": "X", "name": "ReadAsync 2717", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202425, "dur": 1, "ph": "X", "name": "ProcessMessages 1870", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202427, "dur": 55, "ph": "X", "name": "ReadAsync 1870", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202494, "dur": 10, "ph": "X", "name": "ProcessMessages 2297", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202506, "dur": 68, "ph": "X", "name": "ReadAsync 2297", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202579, "dur": 5, "ph": "X", "name": "ProcessMessages 1835", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202605, "dur": 52, "ph": "X", "name": "ReadAsync 1835", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202662, "dur": 1, "ph": "X", "name": "ProcessMessages 2078", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202665, "dur": 36, "ph": "X", "name": "ReadAsync 2078", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202702, "dur": 1, "ph": "X", "name": "ProcessMessages 1438", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202704, "dur": 64, "ph": "X", "name": "ReadAsync 1438", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202770, "dur": 64, "ph": "X", "name": "ReadAsync 1489", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202836, "dur": 1, "ph": "X", "name": "ProcessMessages 1797", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202838, "dur": 35, "ph": "X", "name": "ReadAsync 1797", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202875, "dur": 64, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202941, "dur": 1, "ph": "X", "name": "ProcessMessages 1759", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202942, "dur": 41, "ph": "X", "name": "ReadAsync 1759", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202984, "dur": 1, "ph": "X", "name": "ProcessMessages 1363", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872202986, "dur": 34, "ph": "X", "name": "ReadAsync 1363", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203022, "dur": 39, "ph": "X", "name": "ReadAsync 1195", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203063, "dur": 49, "ph": "X", "name": "ReadAsync 1202", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203115, "dur": 34, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203151, "dur": 33, "ph": "X", "name": "ReadAsync 1228", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203187, "dur": 33, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203226, "dur": 28, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203257, "dur": 44, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203303, "dur": 40, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203344, "dur": 1, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203346, "dur": 26, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203374, "dur": 71, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203446, "dur": 1, "ph": "X", "name": "ProcessMessages 1460", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203448, "dur": 27, "ph": "X", "name": "ReadAsync 1460", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203478, "dur": 17, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203496, "dur": 27, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203544, "dur": 31, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203578, "dur": 54, "ph": "X", "name": "ReadAsync 1289", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203633, "dur": 1, "ph": "X", "name": "ProcessMessages 1341", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203634, "dur": 55, "ph": "X", "name": "ReadAsync 1341", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203691, "dur": 33, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203726, "dur": 63, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203791, "dur": 1, "ph": "X", "name": "ProcessMessages 1413", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203793, "dur": 45, "ph": "X", "name": "ReadAsync 1413", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203840, "dur": 33, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203874, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203876, "dur": 53, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203932, "dur": 9, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203943, "dur": 36, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203980, "dur": 1, "ph": "X", "name": "ProcessMessages 1683", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872203982, "dur": 33, "ph": "X", "name": "ReadAsync 1683", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204017, "dur": 25, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204045, "dur": 25, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204072, "dur": 53, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204128, "dur": 41, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204172, "dur": 39, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204213, "dur": 25, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204241, "dur": 66, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204308, "dur": 1, "ph": "X", "name": "ProcessMessages 1440", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204310, "dur": 29, "ph": "X", "name": "ReadAsync 1440", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204341, "dur": 40, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204383, "dur": 67, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204452, "dur": 1, "ph": "X", "name": "ProcessMessages 1929", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204458, "dur": 38, "ph": "X", "name": "ReadAsync 1929", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204499, "dur": 40, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204539, "dur": 1, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204545, "dur": 39, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204586, "dur": 30, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204617, "dur": 5, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204623, "dur": 37, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204662, "dur": 21, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204686, "dur": 55, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204744, "dur": 75, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204820, "dur": 1, "ph": "X", "name": "ProcessMessages 1307", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204822, "dur": 26, "ph": "X", "name": "ReadAsync 1307", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204850, "dur": 43, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204895, "dur": 18, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204915, "dur": 32, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204949, "dur": 34, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872204985, "dur": 27, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205014, "dur": 34, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205051, "dur": 89, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205141, "dur": 1, "ph": "X", "name": "ProcessMessages 2222", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205143, "dur": 23, "ph": "X", "name": "ReadAsync 2222", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205168, "dur": 28, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205198, "dur": 19, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205218, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205251, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205253, "dur": 15, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205269, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205290, "dur": 52, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205342, "dur": 1, "ph": "X", "name": "ProcessMessages 1617", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205344, "dur": 18, "ph": "X", "name": "ReadAsync 1617", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205364, "dur": 15, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205381, "dur": 26, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205409, "dur": 30, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205442, "dur": 41, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205486, "dur": 19, "ph": "X", "name": "ReadAsync 1443", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205506, "dur": 26, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205540, "dur": 25, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205567, "dur": 27, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205596, "dur": 22, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205621, "dur": 21, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205643, "dur": 22, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205667, "dur": 3, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205671, "dur": 30, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205703, "dur": 14, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205719, "dur": 41, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205762, "dur": 35, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205800, "dur": 16, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205821, "dur": 30, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205853, "dur": 26, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205882, "dur": 37, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205921, "dur": 41, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205963, "dur": 1, "ph": "X", "name": "ProcessMessages 1209", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205965, "dur": 19, "ph": "X", "name": "ReadAsync 1209", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872205986, "dur": 15, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206003, "dur": 20, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206025, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206052, "dur": 15, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206069, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206088, "dur": 33, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206123, "dur": 27, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206152, "dur": 16, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206171, "dur": 22, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206195, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206219, "dur": 14, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206235, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206257, "dur": 40, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206299, "dur": 21, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206322, "dur": 14, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206342, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206367, "dur": 37, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206406, "dur": 143, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206550, "dur": 1, "ph": "X", "name": "ProcessMessages 2060", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206552, "dur": 77, "ph": "X", "name": "ReadAsync 2060", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206633, "dur": 2, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206636, "dur": 77, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206716, "dur": 2, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206719, "dur": 105, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206825, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206827, "dur": 53, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206881, "dur": 64, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872206948, "dur": 459, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207409, "dur": 3, "ph": "X", "name": "ProcessMessages 6656", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207413, "dur": 58, "ph": "X", "name": "ReadAsync 6656", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207473, "dur": 18, "ph": "X", "name": "ReadAsync 1680", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207493, "dur": 96, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207592, "dur": 3, "ph": "X", "name": "ProcessMessages 1288", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207596, "dur": 66, "ph": "X", "name": "ReadAsync 1288", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207665, "dur": 16, "ph": "X", "name": "ProcessMessages 1850", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207683, "dur": 48, "ph": "X", "name": "ReadAsync 1850", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207732, "dur": 1, "ph": "X", "name": "ProcessMessages 1711", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207733, "dur": 18, "ph": "X", "name": "ReadAsync 1711", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207753, "dur": 135, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207891, "dur": 5, "ph": "X", "name": "ProcessMessages 2644", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872207898, "dur": 46, "ph": "X", "name": "ReadAsync 2644", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872208890, "dur": 30, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872208921, "dur": 3, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872208926, "dur": 173, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209101, "dur": 2, "ph": "X", "name": "ProcessMessages 3566", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209103, "dur": 24, "ph": "X", "name": "ReadAsync 3566", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209129, "dur": 126, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209258, "dur": 4, "ph": "X", "name": "ProcessMessages 2804", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209264, "dur": 56, "ph": "X", "name": "ReadAsync 2804", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209323, "dur": 2, "ph": "X", "name": "ProcessMessages 1761", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209326, "dur": 73, "ph": "X", "name": "ReadAsync 1761", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209401, "dur": 1, "ph": "X", "name": "ProcessMessages 1912", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209403, "dur": 61, "ph": "X", "name": "ReadAsync 1912", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209466, "dur": 2, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209470, "dur": 59, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209532, "dur": 2, "ph": "X", "name": "ProcessMessages 1253", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209535, "dur": 54, "ph": "X", "name": "ReadAsync 1253", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209591, "dur": 3, "ph": "X", "name": "ProcessMessages 1719", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209595, "dur": 56, "ph": "X", "name": "ReadAsync 1719", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209653, "dur": 1, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209655, "dur": 27, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209684, "dur": 95, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209780, "dur": 1, "ph": "X", "name": "ProcessMessages 1449", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209782, "dur": 54, "ph": "X", "name": "ReadAsync 1449", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209837, "dur": 1, "ph": "X", "name": "ProcessMessages 1413", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209838, "dur": 32, "ph": "X", "name": "ReadAsync 1413", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209872, "dur": 69, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209944, "dur": 20, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872209966, "dur": 60, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210027, "dur": 1, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210028, "dur": 17, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210048, "dur": 204, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210254, "dur": 1, "ph": "X", "name": "ProcessMessages 1728", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210256, "dur": 28, "ph": "X", "name": "ReadAsync 1728", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210286, "dur": 174, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210463, "dur": 4, "ph": "X", "name": "ProcessMessages 1836", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210468, "dur": 51, "ph": "X", "name": "ReadAsync 1836", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210521, "dur": 51, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210574, "dur": 33, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210610, "dur": 86, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210698, "dur": 2, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210701, "dur": 52, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210755, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210756, "dur": 199, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210958, "dur": 4, "ph": "X", "name": "ProcessMessages 2580", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872210964, "dur": 105, "ph": "X", "name": "ReadAsync 2580", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211070, "dur": 1, "ph": "X", "name": "ProcessMessages 2088", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211072, "dur": 98, "ph": "X", "name": "ReadAsync 2088", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211172, "dur": 19, "ph": "X", "name": "ReadAsync 1035", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211199, "dur": 38, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211239, "dur": 62, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211305, "dur": 9, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211315, "dur": 257, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211573, "dur": 1, "ph": "X", "name": "ProcessMessages 1365", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211575, "dur": 36, "ph": "X", "name": "ReadAsync 1365", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211614, "dur": 36, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211657, "dur": 49, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211708, "dur": 18, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211728, "dur": 107, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211850, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211852, "dur": 40, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211895, "dur": 69, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872211966, "dur": 125, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212093, "dur": 1, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212094, "dur": 238, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212337, "dur": 3, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212343, "dur": 193, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212537, "dur": 1, "ph": "X", "name": "ProcessMessages 2055", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212546, "dur": 34, "ph": "X", "name": "ReadAsync 2055", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212583, "dur": 71, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212657, "dur": 1, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212660, "dur": 133, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212795, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872212798, "dur": 331, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213131, "dur": 2, "ph": "X", "name": "ProcessMessages 2888", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213134, "dur": 155, "ph": "X", "name": "ReadAsync 2888", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213290, "dur": 1, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213291, "dur": 64, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213364, "dur": 34, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213400, "dur": 253, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213655, "dur": 1, "ph": "X", "name": "ProcessMessages 2064", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213657, "dur": 153, "ph": "X", "name": "ReadAsync 2064", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213814, "dur": 2, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213818, "dur": 75, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213894, "dur": 1, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213896, "dur": 29, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213931, "dur": 39, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213972, "dur": 18, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872213992, "dur": 91, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214086, "dur": 87, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214175, "dur": 38, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214216, "dur": 73, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214291, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214315, "dur": 79, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214396, "dur": 64, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214462, "dur": 20, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214483, "dur": 55, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214541, "dur": 98, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214640, "dur": 2, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214643, "dur": 35, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214681, "dur": 109, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214791, "dur": 1, "ph": "X", "name": "ProcessMessages 1409", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214793, "dur": 56, "ph": "X", "name": "ReadAsync 1409", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214856, "dur": 50, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214909, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214913, "dur": 81, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214995, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872214996, "dur": 106, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215103, "dur": 1, "ph": "X", "name": "ProcessMessages 1557", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215105, "dur": 49, "ph": "X", "name": "ReadAsync 1557", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215156, "dur": 22, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215179, "dur": 369, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215550, "dur": 39, "ph": "X", "name": "ProcessMessages 4890", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215591, "dur": 92, "ph": "X", "name": "ReadAsync 4890", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215685, "dur": 1, "ph": "X", "name": "ProcessMessages 2068", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215687, "dur": 40, "ph": "X", "name": "ReadAsync 2068", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215730, "dur": 32, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215765, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215767, "dur": 108, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215878, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215914, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872215972, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216092, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216153, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216154, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216220, "dur": 211, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216432, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216434, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216622, "dur": 12, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216635, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216679, "dur": 246, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872216927, "dur": 230, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217161, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217234, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217237, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217340, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217464, "dur": 202, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217667, "dur": 99, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217772, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217891, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872217970, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218048, "dur": 133, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218184, "dur": 124, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218314, "dur": 134, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218450, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218509, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218590, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218592, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218749, "dur": 150, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218901, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872218927, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219059, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219078, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219199, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219273, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219299, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219424, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219519, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219574, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219666, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219766, "dur": 162, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872219931, "dur": 89, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220023, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220026, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220087, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220184, "dur": 71, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220258, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220311, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220387, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220414, "dur": 97, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220513, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220565, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220681, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220734, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220737, "dur": 138, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220878, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872220918, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221090, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221114, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221158, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221237, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221240, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221309, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221408, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221474, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221610, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221683, "dur": 125, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221810, "dur": 87, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221901, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872221918, "dur": 215, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222136, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222139, "dur": 42, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222184, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222248, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222340, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222423, "dur": 301, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222727, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222784, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222894, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222902, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872222963, "dur": 74, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223039, "dur": 86, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223127, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223143, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223222, "dur": 83, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223310, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223314, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223360, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223422, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223549, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223629, "dur": 80, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223711, "dur": 64, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223779, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223890, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223936, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872223976, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224008, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224108, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224114, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224141, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224170, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224208, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224274, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224373, "dur": 56, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224431, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224479, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224583, "dur": 69, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224654, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224705, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224731, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224791, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224843, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224858, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872224900, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225003, "dur": 70, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225075, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225098, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225122, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225149, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225254, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225278, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225402, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225458, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225514, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225539, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225587, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225609, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225630, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225677, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225718, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225826, "dur": 156, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225983, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872225985, "dur": 321, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872226307, "dur": 44, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872226351, "dur": 14690, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872241047, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872241049, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872241234, "dur": 2590, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872243839, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872243842, "dur": 328, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872244173, "dur": 243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872244426, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872244482, "dur": 2993, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872247479, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872247481, "dur": 17257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872264745, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872264748, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872264800, "dur": 434, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872265241, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872265246, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872265316, "dur": 312, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872265632, "dur": 238, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872265872, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872266032, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872266218, "dur": 322, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872266554, "dur": 57, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872266613, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872266853, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872266854, "dur": 460, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872267317, "dur": 219, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872267538, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872267585, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872267635, "dur": 354, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872267996, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872267999, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872268165, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872268353, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872268360, "dur": 197, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872268559, "dur": 305, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872268866, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269021, "dur": 388, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269412, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269546, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269550, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269669, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269758, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269916, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872269919, "dur": 280, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872270202, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872270448, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872270450, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872270605, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872270607, "dur": 391, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872271003, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872271006, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872271201, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872271203, "dur": 254, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872271460, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872271645, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872271742, "dur": 525, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872272270, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872272459, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872272651, "dur": 219, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872272872, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273106, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273112, "dur": 198, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273313, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273419, "dur": 229, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273651, "dur": 129, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273782, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273809, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273944, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872273947, "dur": 386, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872274337, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872274339, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872274403, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872274650, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872274678, "dur": 215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872274896, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872275001, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872275002, "dur": 159, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872275163, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872275193, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872275427, "dur": 237, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872275666, "dur": 346, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872276014, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872276144, "dur": 412, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872276558, "dur": 461, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872277022, "dur": 311, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872277335, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872277556, "dur": 113, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872277722, "dur": 251, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872277997, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872278088, "dur": 220, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872278312, "dur": 35, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872278350, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872278459, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872278652, "dur": 57, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872278710, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872278716, "dur": 473, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872279194, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872279198, "dur": 346, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872279547, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872279548, "dur": 401, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872279952, "dur": 300, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872280254, "dur": 1972, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872282231, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872282233, "dur": 246, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872282486, "dur": 7, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872282504, "dur": 363, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872282870, "dur": 3670, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872286546, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872286549, "dur": 1451, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872288008, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872288076, "dur": 156, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872288234, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872288298, "dur": 515, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872288816, "dur": 722, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872289543, "dur": 216, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872289762, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872289971, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872290065, "dur": 417, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872290484, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872290486, "dur": 156457, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872446955, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872446961, "dur": 346, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872447313, "dur": 269, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872447586, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872447588, "dur": 1625, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872449217, "dur": 1, "ph": "X", "name": "ProcessMessages 7846", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872450317, "dur": 675, "ph": "X", "name": "ReadAsync 7846", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872450996, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872450998, "dur": 92, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872451092, "dur": 5396, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872456501, "dur": 37, "ph": "X", "name": "ProcessMessages 2099", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872456540, "dur": 4361, "ph": "X", "name": "ReadAsync 2099", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872460912, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872460918, "dur": 2472, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872463403, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872463409, "dur": 2161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872465583, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872465589, "dur": 215, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872465808, "dur": 1753, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872467570, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872467573, "dur": 1306, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872468887, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872468891, "dur": 2300, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872471202, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872471207, "dur": 2817, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872474034, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872474040, "dur": 1218, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872475263, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872475265, "dur": 3797, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872479069, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872479072, "dur": 2819, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872481898, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872481901, "dur": 4696, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872486609, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872486615, "dur": 4549, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872491173, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872491176, "dur": 1535, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872492722, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872492727, "dur": 374, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872493105, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872493108, "dur": 195, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872493308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872493310, "dur": 967, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872494285, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872494289, "dur": 9283, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872503581, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872503586, "dur": 4028, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872507621, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872507624, "dur": 2197, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872509828, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872509831, "dur": 858, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872510693, "dur": 2487, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872513192, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872513197, "dur": 1169, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872514371, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872514373, "dur": 590, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872514968, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872514971, "dur": 1672, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872516648, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872516650, "dur": 1222, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872517876, "dur": 895, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872518774, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872518952, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872519107, "dur": 171, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872519280, "dur": 226, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872519508, "dur": 184, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872519696, "dur": 426, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520124, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520165, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520272, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520356, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520491, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520575, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520735, "dur": 117, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520854, "dur": 112, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872520968, "dur": 162, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521131, "dur": 8, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521141, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521180, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521297, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521340, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521508, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521582, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521742, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521833, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872521940, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522133, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522138, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522195, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522229, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522465, "dur": 120, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522588, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522630, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522660, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522799, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522819, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522863, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872522930, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523118, "dur": 194, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523316, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523320, "dur": 77, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523400, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523491, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523672, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523711, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872523812, "dur": 210, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524025, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524028, "dur": 358, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524388, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524417, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524551, "dur": 15, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524568, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524664, "dur": 263, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872524929, "dur": 76870, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872601806, "dur": 18, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872601826, "dur": 3026, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872604859, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872604862, "dur": 125654, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872730524, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872730527, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872730584, "dur": 85, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872730673, "dur": 60, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872730735, "dur": 204, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872730966, "dur": 22, "ph": "X", "name": "ProcessMessages 4167", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872730989, "dur": 3109, "ph": "X", "name": "ReadAsync 4167", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872734105, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872734109, "dur": 264, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872734375, "dur": 24, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872734400, "dur": 190, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 75155, "tid": 34359738368, "ts": 1750860872734593, "dur": 5515, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872741119, "dur": 1385, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 75155, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 75155, "tid": 30064771072, "ts": 1750860872024948, "dur": 70, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 75155, "tid": 30064771072, "ts": 1750860872025019, "dur": 11299, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 75155, "tid": 30064771072, "ts": 1750860872036319, "dur": 67, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872742510, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 75155, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 75155, "tid": 1, "ts": 1750860870765766, "dur": 3048, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 75155, "tid": 1, "ts": 1750860870768822, "dur": 30525, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 75155, "tid": 1, "ts": 1750860870799350, "dur": 27504, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872742518, "dur": 4, "ph": "X", "name": "", "args": {}}, {"pid": 75155, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870765640, "dur": 12894, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870778540, "dur": 55675, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870778776, "dur": 99, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870778900, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870779008, "dur": 31, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870779042, "dur": 2187, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781272, "dur": 4, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781278, "dur": 21, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781300, "dur": 1, "ph": "X", "name": "ProcessMessages 1705", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781302, "dur": 18, "ph": "X", "name": "ReadAsync 1705", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781322, "dur": 19, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781353, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781355, "dur": 45, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781408, "dur": 46, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781459, "dur": 1, "ph": "X", "name": "ProcessMessages 1722", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781461, "dur": 44, "ph": "X", "name": "ReadAsync 1722", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781507, "dur": 1, "ph": "X", "name": "ProcessMessages 1312", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781508, "dur": 33, "ph": "X", "name": "ReadAsync 1312", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781545, "dur": 1, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781548, "dur": 112, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781665, "dur": 1, "ph": "X", "name": "ProcessMessages 2670", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781669, "dur": 38, "ph": "X", "name": "ReadAsync 2670", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781712, "dur": 36, "ph": "X", "name": "ReadAsync 1338", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781752, "dur": 193, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781946, "dur": 4, "ph": "X", "name": "ProcessMessages 4721", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781951, "dur": 29, "ph": "X", "name": "ReadAsync 4721", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781982, "dur": 2, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870781986, "dur": 43, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782030, "dur": 1, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782032, "dur": 42, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782097, "dur": 1, "ph": "X", "name": "ProcessMessages 1186", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782098, "dur": 24, "ph": "X", "name": "ReadAsync 1186", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782124, "dur": 1, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782125, "dur": 43, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782178, "dur": 31, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782211, "dur": 43, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782268, "dur": 1, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782269, "dur": 42, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782313, "dur": 1, "ph": "X", "name": "ProcessMessages 1414", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782316, "dur": 39, "ph": "X", "name": "ReadAsync 1414", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782359, "dur": 1, "ph": "X", "name": "ProcessMessages 1296", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782360, "dur": 36, "ph": "X", "name": "ReadAsync 1296", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782399, "dur": 57, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782457, "dur": 1, "ph": "X", "name": "ProcessMessages 1738", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782462, "dur": 38, "ph": "X", "name": "ReadAsync 1738", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782503, "dur": 25, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782529, "dur": 3, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782536, "dur": 38, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782579, "dur": 38, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782632, "dur": 30, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782664, "dur": 35, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782700, "dur": 26, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782730, "dur": 1, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782732, "dur": 42, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782776, "dur": 1, "ph": "X", "name": "ProcessMessages 1190", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782778, "dur": 62, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782841, "dur": 1, "ph": "X", "name": "ProcessMessages 1697", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782842, "dur": 27, "ph": "X", "name": "ReadAsync 1697", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782871, "dur": 38, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870782912, "dur": 55, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783005, "dur": 1, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783012, "dur": 153, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783166, "dur": 10, "ph": "X", "name": "ProcessMessages 3362", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783177, "dur": 21, "ph": "X", "name": "ReadAsync 3362", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783203, "dur": 69, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783275, "dur": 1, "ph": "X", "name": "ProcessMessages 1687", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783278, "dur": 69, "ph": "X", "name": "ReadAsync 1687", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783348, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783376, "dur": 21, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783398, "dur": 1, "ph": "X", "name": "ProcessMessages 1428", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783401, "dur": 17, "ph": "X", "name": "ReadAsync 1428", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783422, "dur": 20, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783445, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783466, "dur": 31, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783501, "dur": 22, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783528, "dur": 24, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783554, "dur": 30, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783587, "dur": 20, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783610, "dur": 24, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783638, "dur": 26, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783666, "dur": 16, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783685, "dur": 20, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783738, "dur": 28, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783767, "dur": 1, "ph": "X", "name": "ProcessMessages 1510", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783769, "dur": 20, "ph": "X", "name": "ReadAsync 1510", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783825, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783846, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783847, "dur": 28, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783879, "dur": 17, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783905, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783932, "dur": 23, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783957, "dur": 23, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870783985, "dur": 36, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784024, "dur": 22, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784050, "dur": 22, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784075, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784077, "dur": 42, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784121, "dur": 19, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784144, "dur": 19, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784165, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784194, "dur": 285, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784482, "dur": 43, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784526, "dur": 3, "ph": "X", "name": "ProcessMessages 7382", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784555, "dur": 21, "ph": "X", "name": "ReadAsync 7382", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784578, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784579, "dur": 46, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784627, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784628, "dur": 57, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784686, "dur": 1, "ph": "X", "name": "ProcessMessages 1538", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784690, "dur": 32, "ph": "X", "name": "ReadAsync 1538", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784725, "dur": 33, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784760, "dur": 40, "ph": "X", "name": "ReadAsync 1298", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784804, "dur": 21, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784827, "dur": 38, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784866, "dur": 1, "ph": "X", "name": "ProcessMessages 1298", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784868, "dur": 83, "ph": "X", "name": "ReadAsync 1298", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784954, "dur": 21, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784981, "dur": 1, "ph": "X", "name": "ProcessMessages 2067", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870784983, "dur": 23, "ph": "X", "name": "ReadAsync 2067", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785014, "dur": 32, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785047, "dur": 1, "ph": "X", "name": "ProcessMessages 1272", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785049, "dur": 27, "ph": "X", "name": "ReadAsync 1272", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785081, "dur": 45, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785132, "dur": 1, "ph": "X", "name": "ProcessMessages 1345", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785134, "dur": 34, "ph": "X", "name": "ReadAsync 1345", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785170, "dur": 66, "ph": "X", "name": "ReadAsync 1175", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785237, "dur": 1, "ph": "X", "name": "ProcessMessages 1776", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785239, "dur": 19, "ph": "X", "name": "ReadAsync 1776", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785260, "dur": 33, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785323, "dur": 22, "ph": "X", "name": "ReadAsync 1094", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785346, "dur": 1, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785356, "dur": 19, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785378, "dur": 25, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785406, "dur": 63, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785475, "dur": 34, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785511, "dur": 1, "ph": "X", "name": "ProcessMessages 1766", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785513, "dur": 16, "ph": "X", "name": "ReadAsync 1766", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785531, "dur": 3, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785535, "dur": 83, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785619, "dur": 1, "ph": "X", "name": "ProcessMessages 2068", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785621, "dur": 68, "ph": "X", "name": "ReadAsync 2068", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785696, "dur": 1, "ph": "X", "name": "ProcessMessages 1877", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785698, "dur": 18, "ph": "X", "name": "ReadAsync 1877", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785718, "dur": 30, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785758, "dur": 19, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785784, "dur": 89, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785909, "dur": 1, "ph": "X", "name": "ProcessMessages 1608", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785910, "dur": 25, "ph": "X", "name": "ReadAsync 1608", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785939, "dur": 22, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785962, "dur": 7, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785971, "dur": 21, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870785999, "dur": 29, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786030, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786032, "dur": 36, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786071, "dur": 51, "ph": "X", "name": "ReadAsync 1009", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786123, "dur": 1, "ph": "X", "name": "ProcessMessages 1223", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786124, "dur": 25, "ph": "X", "name": "ReadAsync 1223", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786180, "dur": 5, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786186, "dur": 24, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786213, "dur": 1, "ph": "X", "name": "ProcessMessages 1677", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786214, "dur": 28, "ph": "X", "name": "ReadAsync 1677", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786244, "dur": 5, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786250, "dur": 66, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786317, "dur": 1, "ph": "X", "name": "ProcessMessages 1800", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786319, "dur": 23, "ph": "X", "name": "ReadAsync 1800", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786384, "dur": 60, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786447, "dur": 1, "ph": "X", "name": "ProcessMessages 2119", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786449, "dur": 69, "ph": "X", "name": "ReadAsync 2119", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786519, "dur": 1, "ph": "X", "name": "ProcessMessages 2107", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786521, "dur": 71, "ph": "X", "name": "ReadAsync 2107", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786593, "dur": 1, "ph": "X", "name": "ProcessMessages 1481", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786595, "dur": 62, "ph": "X", "name": "ReadAsync 1481", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786660, "dur": 38, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786704, "dur": 50, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786755, "dur": 1, "ph": "X", "name": "ProcessMessages 1264", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786757, "dur": 21, "ph": "X", "name": "ReadAsync 1264", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786784, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786809, "dur": 39, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786850, "dur": 25, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786916, "dur": 30, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786947, "dur": 1, "ph": "X", "name": "ProcessMessages 1693", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786950, "dur": 30, "ph": "X", "name": "ReadAsync 1693", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870786985, "dur": 26, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787012, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787014, "dur": 193, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787209, "dur": 2, "ph": "X", "name": "ProcessMessages 4527", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787211, "dur": 21, "ph": "X", "name": "ReadAsync 4527", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787238, "dur": 29, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787269, "dur": 49, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787321, "dur": 36, "ph": "X", "name": "ReadAsync 1365", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787358, "dur": 1, "ph": "X", "name": "ProcessMessages 1019", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787361, "dur": 17, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787380, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787521, "dur": 33, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787556, "dur": 1, "ph": "X", "name": "ProcessMessages 3770", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787559, "dur": 17, "ph": "X", "name": "ReadAsync 3770", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787577, "dur": 4, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787582, "dur": 58, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787690, "dur": 9, "ph": "X", "name": "ProcessMessages 1619", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787706, "dur": 80, "ph": "X", "name": "ReadAsync 1619", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787794, "dur": 2, "ph": "X", "name": "ProcessMessages 3716", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787796, "dur": 25, "ph": "X", "name": "ReadAsync 3716", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787824, "dur": 36, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787864, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787866, "dur": 75, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787951, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787954, "dur": 21, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787984, "dur": 1, "ph": "X", "name": "ProcessMessages 1431", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870787986, "dur": 39, "ph": "X", "name": "ReadAsync 1431", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788029, "dur": 29, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788068, "dur": 36, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788106, "dur": 22, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788131, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788160, "dur": 19, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788181, "dur": 79, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788269, "dur": 81, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788381, "dur": 71, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788460, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788466, "dur": 44, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788514, "dur": 1, "ph": "X", "name": "ProcessMessages 1261", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788516, "dur": 22, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788540, "dur": 49, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788592, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788594, "dur": 30, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788625, "dur": 2, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788628, "dur": 18, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788650, "dur": 33, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788685, "dur": 67, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788758, "dur": 139, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788932, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788934, "dur": 57, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788995, "dur": 1, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870788997, "dur": 41, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789040, "dur": 31, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789076, "dur": 37, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789125, "dur": 21, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789148, "dur": 22, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789173, "dur": 74, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789249, "dur": 51, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789302, "dur": 25, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789333, "dur": 35, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789371, "dur": 3, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789374, "dur": 110, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789485, "dur": 1, "ph": "X", "name": "ProcessMessages 1164", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789487, "dur": 22, "ph": "X", "name": "ReadAsync 1164", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789536, "dur": 24, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789562, "dur": 56, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789619, "dur": 1, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789621, "dur": 29, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789662, "dur": 89, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789767, "dur": 11, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789782, "dur": 35, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789818, "dur": 3, "ph": "X", "name": "ProcessMessages 1560", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789826, "dur": 132, "ph": "X", "name": "ReadAsync 1560", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789964, "dur": 1, "ph": "X", "name": "ProcessMessages 1766", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789966, "dur": 17, "ph": "X", "name": "ReadAsync 1766", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870789988, "dur": 66, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790074, "dur": 23, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790099, "dur": 25, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790126, "dur": 28, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790155, "dur": 4, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790160, "dur": 23, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790186, "dur": 109, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790296, "dur": 50, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790348, "dur": 179, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790533, "dur": 1, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790535, "dur": 101, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790638, "dur": 2, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790641, "dur": 164, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790812, "dur": 1, "ph": "X", "name": "ProcessMessages 1778", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790814, "dur": 35, "ph": "X", "name": "ReadAsync 1778", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790850, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790852, "dur": 36, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870790890, "dur": 156, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791048, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791049, "dur": 33, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791085, "dur": 49, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791163, "dur": 5, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791272, "dur": 39, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791317, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791319, "dur": 20, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791342, "dur": 40, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791388, "dur": 76, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791467, "dur": 55, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791541, "dur": 65, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791608, "dur": 93, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791704, "dur": 20, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791727, "dur": 45, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791775, "dur": 20, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791797, "dur": 21, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791820, "dur": 82, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791909, "dur": 70, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870791982, "dur": 85, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792079, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792081, "dur": 31, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792115, "dur": 3, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792119, "dur": 22, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792145, "dur": 60, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792213, "dur": 76, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792295, "dur": 32, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792357, "dur": 6, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792367, "dur": 39, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792407, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792409, "dur": 18, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792430, "dur": 55, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792487, "dur": 30, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792521, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792548, "dur": 37, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792587, "dur": 3, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792600, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792620, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792622, "dur": 19, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792646, "dur": 96, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792745, "dur": 3, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792749, "dur": 15, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792766, "dur": 21, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792790, "dur": 16, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792808, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792829, "dur": 34, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792869, "dur": 107, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870792981, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793002, "dur": 18, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793022, "dur": 17, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793041, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793065, "dur": 31, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793099, "dur": 17, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793118, "dur": 134, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793261, "dur": 400, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793663, "dur": 189, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793854, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793926, "dur": 22, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793960, "dur": 10, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870793971, "dur": 62, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794035, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794075, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794257, "dur": 136, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794402, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794464, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794495, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794590, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794682, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794718, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794739, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794852, "dur": 122, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870794977, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795010, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795041, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795069, "dur": 147, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795219, "dur": 73, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795294, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795349, "dur": 81, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795432, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795574, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795610, "dur": 75, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795687, "dur": 103, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795793, "dur": 114, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795916, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870795963, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796005, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796070, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796167, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796212, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796342, "dur": 85, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796428, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796432, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796453, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796551, "dur": 84, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796647, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796705, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796754, "dur": 104, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796859, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796862, "dur": 78, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870796944, "dur": 116, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797062, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797066, "dur": 100, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797168, "dur": 13, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797183, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797293, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797350, "dur": 12, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797364, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797379, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797405, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797446, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797470, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797494, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797555, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797577, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797675, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797701, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797720, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797738, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797769, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797818, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797854, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797902, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797951, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870797977, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798040, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798063, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798085, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798127, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798167, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798196, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798266, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798308, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798382, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798430, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798484, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798551, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798576, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798656, "dur": 58, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798716, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798744, "dur": 71, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798818, "dur": 71, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870798891, "dur": 121, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799014, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799034, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799035, "dur": 17, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799054, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799055, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799077, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799113, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799175, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799204, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799284, "dur": 79, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799365, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799392, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799431, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799449, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799490, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799563, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799641, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799686, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799728, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799787, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870799948, "dur": 9723, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870809675, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870809676, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870809913, "dur": 1815, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870811730, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870811884, "dur": 471, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870812403, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870812425, "dur": 1979, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870814406, "dur": 9286, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870823697, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870823699, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870823743, "dur": 340, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870824085, "dur": 263, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870824351, "dur": 283, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870824636, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870824721, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870824894, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870824986, "dur": 19, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870825007, "dur": 464, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870825473, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870825570, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870825721, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870825827, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870825962, "dur": 181, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870826145, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870826240, "dur": 465, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870826707, "dur": 126, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870826836, "dur": 232, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827069, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827075, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827307, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827388, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827463, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827548, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827575, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827666, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827754, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 75155, "tid": 25769803776, "ts": 1750860870827755, "dur": 6451, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872742524, "dur": 741, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 75155, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 75155, "tid": 21474836480, "ts": 1750860870765242, "dur": 61616, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 75155, "tid": 21474836480, "ts": 1750860870826860, "dur": 43, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872743267, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 75155, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 75155, "tid": 17179869184, "ts": 1750860870728337, "dur": 106054, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1750860870728566, "dur": 36301, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1750860870834414, "dur": 1168904, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1750860872003489, "dur": 736818, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1750860872008472, "dur": 16307, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1750860872740312, "dur": 120, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 75155, "tid": 17179869184, "ts": 1750860872740332, "dur": 74, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872743278, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750860872036589, "dur": 165310, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872201910, "dur": 263, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872202250, "dur": 80, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872202564, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_C5F3E4FEF96D893F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750860872202729, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750860872207076, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750860872207334, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750860872208799, "dur": 670, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750860872210355, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750860872211874, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750860872212037, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1750860872213067, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750860872214063, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750860872214704, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1750860872202335, "dur": 13919, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872216262, "dur": 518602, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872734902, "dur": 60, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872735004, "dur": 59, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872735068, "dur": 385, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750860872202285, "dur": 13989, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872216729, "dur": 270, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1750860872216999, "dur": 1647, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1750860872218646, "dur": 656, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1750860872216278, "dur": 3024, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872219303, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0462768E64EABD0A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872219478, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872219581, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FF3C62C209E1F2AF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872219782, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872219903, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A6AABF0E6ADA0B25.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872220064, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872220155, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA115901BA18BB2D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872220264, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872220414, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_06C0A05E69B9BD23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872220639, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_EB44D0B764B7C8DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872220696, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872220781, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_3FB038AB94CBD0EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872220899, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872221004, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_ED2AB5385077AD72.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872221135, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872221259, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_857F5E2A119BA0C9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872221434, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872221523, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872221634, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872221789, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872221908, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872222033, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872222172, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872222247, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872222403, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750860872222481, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872222695, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872222822, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872223023, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872223202, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872223298, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872223392, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872223550, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872223685, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872223800, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872223961, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224080, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224159, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224220, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224354, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224474, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224598, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224737, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224864, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872224931, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225045, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225125, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225202, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225311, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225399, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225501, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225587, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225641, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225724, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225828, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872225927, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872226005, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872226073, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872226156, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872226312, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872227175, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872227859, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872229305, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872230174, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872231178, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872232135, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872233112, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872233982, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872235005, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872236013, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872237246, "dur": 2187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872239434, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872241374, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872242726, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872243645, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872245106, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872246603, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872247285, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872248167, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872248958, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872249659, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872250397, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872251123, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872251848, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872252584, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872253312, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872254908, "dur": 3159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872258067, "dur": 3242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872261309, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872263243, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872264752, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872265169, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872266093, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872266361, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872266427, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872266659, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872266764, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872267770, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872268030, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872268109, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872268193, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872268342, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872268925, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872268998, "dur": 2665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872271664, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872271793, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872272220, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872272329, "dur": 1558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872273888, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872274152, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872274234, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872274399, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872275605, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872275683, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872276144, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872276574, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872276686, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872277262, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872277390, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872278333, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872278573, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872278732, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872278796, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872278942, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872279027, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872279143, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872279274, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872280098, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872280162, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872281328, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872281382, "dur": 1374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872282760, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872282889, "dur": 4017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872286907, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872287043, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872287134, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860872287734, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872287816, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872288216, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872288678, "dur": 164153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872452834, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872455065, "dur": 1683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872456774, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872459175, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872459436, "dur": 31530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872490968, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872491170, "dur": 22122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872513293, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860872513437, "dur": 2989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872516443, "dur": 8657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750860872525125, "dur": 209725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872202290, "dur": 13995, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872216458, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D7331C31AE624D95.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872216588, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872216705, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_FBD6848B40F9D84E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872216897, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872217147, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_479F21D64DDF5A5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872217348, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872217664, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FAD28CEFAD4EF7CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872217852, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872217967, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_110B7A9752F5AD38.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872218108, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872218316, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_C268CA3985788B50.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872218490, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872218675, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5A04CF1DC9DD1390.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872218903, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872219041, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C30E9AD099929D47.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872219279, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872219413, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A49295611C727860.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872219619, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872219771, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_165FE63856186A10.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872219931, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872220053, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A3EA01C4850945EE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872220206, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872220322, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6799CBAE2582DA9C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872220509, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_A79420259383E98A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872220689, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872220759, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2CC8B0A3337F4D14.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872220896, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872221025, "dur": 5035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872226060, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872226119, "dur": 15098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872241218, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872241408, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872241496, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872241626, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872242828, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872243732, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872245198, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872246696, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872247367, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872248255, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872249063, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872249766, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872250486, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872251247, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872251945, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872252695, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872253412, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872255277, "dur": 3252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872258530, "dur": 2903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872261433, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872263418, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872264129, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872264663, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872264752, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872265127, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872266066, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872266852, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872267057, "dur": 1771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872268829, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872269048, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872269272, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872269724, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872269948, "dur": 1476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872271425, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872271554, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872271709, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872271850, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872271949, "dur": 1186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872273136, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872273343, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872273662, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872273735, "dur": 1867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872275602, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872275889, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872275966, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872276034, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872276168, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872276310, "dur": 1467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872277778, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872277927, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872278074, "dur": 4485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872282625, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872282758, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872283971, "dur": 1636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872285653, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872285763, "dur": 1221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872287114, "dur": 1682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860872288810, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872289247, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872289674, "dur": 165453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872455128, "dur": 12701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872467831, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872468040, "dur": 4164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872472206, "dur": 932, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872473160, "dur": 8213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872481373, "dur": 1830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872483213, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872484294, "dur": 1444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872485756, "dur": 7958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872493715, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872493908, "dur": 12421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872506331, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872506859, "dur": 4263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872511122, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872511256, "dur": 3744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872515001, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872515076, "dur": 2897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872517974, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872518085, "dur": 6721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750860872524807, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872524897, "dur": 209553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860872734498, "dur": 321, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750860872202297, "dur": 14013, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872216431, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_C4615A0A510AFA65.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872216492, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872216614, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_A8B77A263A174862.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872216784, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872217010, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E4ECE7E8BB35882.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872217171, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872217320, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_54E2C669D74DB25A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872217587, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872217690, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_79266F7FBDBF49B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872217855, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872217961, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C9234425C371F41A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872218106, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872218231, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872218443, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872218554, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872218631, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_845B1BE9B09512AE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872218855, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872219000, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_070DA0E44072E4AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872219226, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872219358, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A07FB6BEB1E7333E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872219556, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872219707, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8D8F81C2AD007FF4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872219873, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872219987, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DBE3A542E9C167DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872220145, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872220232, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4574CE91C336EF7D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872220410, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872220550, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D0B6A15278A42C4B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872220666, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872220753, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_82FEB68469624F24.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872220873, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872220963, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8C284CAFF6588524.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872221120, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872221223, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6C262D80C93BD4F0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872221438, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872221591, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872221714, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872221929, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872222114, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872222291, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872222370, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872222557, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872222764, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872222916, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872223112, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872223284, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872223386, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872223555, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872223692, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872223786, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872223939, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224106, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224179, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224259, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224431, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224517, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224682, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224801, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224890, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872224948, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225076, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225178, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225303, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225387, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225445, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225554, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225615, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225712, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225799, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225912, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872225993, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872226059, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872226116, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872226220, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872226320, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872227149, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872227837, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872229255, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872230135, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872231022, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872232012, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872232964, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872233864, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872234739, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872235845, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872236943, "dur": 2274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872239217, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872241036, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872242502, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872243399, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872244811, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872244878, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872246493, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872247143, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872247959, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872248795, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872249529, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872250262, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872250995, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872251712, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872252438, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872253146, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872254613, "dur": 3025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872257639, "dur": 3335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872260974, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872261716, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872263628, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872264369, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872264737, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872265122, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872265702, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872265807, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872267032, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872267196, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872267562, "dur": 1649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872269212, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872269400, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860872269710, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872269769, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872270688, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872270812, "dur": 1892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1750860872272811, "dur": 626, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872446334, "dur": 5307, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872273457, "dur": 178196, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1750860872452819, "dur": 17556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872470375, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872470499, "dur": 3875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872474374, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872474691, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872477025, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872477303, "dur": 3453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872480757, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872481000, "dur": 10538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872491539, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872492132, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872492534, "dur": 3090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872495625, "dur": 846, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872496495, "dur": 21060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872517571, "dur": 7164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750860872524805, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860872525409, "dur": 209435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872202305, "dur": 14021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872216462, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_C5F3E4FEF96D893F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872216570, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872216683, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_F0C4CE25855A5940.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872216842, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872217131, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_013EC6C429305933.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872217350, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872217656, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872217857, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872217970, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4475AF2AFFABA926.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872218131, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0D7C2BFD403ED278.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872218353, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872218460, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_6C9DA28BBAE3FB82.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872218668, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872218789, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872218996, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872219178, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_34F8FBC3386DE99E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872219371, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872219537, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_462079EBF000A463.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872219751, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872219853, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_65DFED6065744ECD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872220034, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872220137, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872220326, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872220429, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A8529918C73873A7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872220557, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872220630, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_0897B8A47BC70881.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872220747, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872220866, "dur": 5079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872225980, "dur": 17998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872243978, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872244215, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872244426, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872244534, "dur": 3339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872247924, "dur": 17077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872265113, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872265189, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872265275, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872265683, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872265742, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872266079, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872266258, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872266558, "dur": 2889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872269448, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872269611, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872270187, "dur": 2034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872272221, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872272851, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872272974, "dur": 1513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872274537, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872275918, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872276025, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872276115, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872276468, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872276548, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872279202, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872279845, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872282230, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872282628, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872282826, "dur": 4300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872287126, "dur": 2029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860872289155, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872289277, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872289998, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872290228, "dur": 165112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872455342, "dur": 10848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872466191, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872466381, "dur": 13129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872479511, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872479720, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872481373, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872481612, "dur": 11419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872493042, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872493249, "dur": 14774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872508024, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872508236, "dur": 5305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872513542, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872513691, "dur": 3456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872517147, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872517234, "dur": 6228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750860872523463, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872523551, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750860872523608, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872523787, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872523990, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872524288, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750860872524363, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872524446, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860872524875, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750860872524953, "dur": 209903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872202312, "dur": 14030, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872216436, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_9EFE93A4B6B28B73.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872216515, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872216632, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_7D5EE36C09E1C813.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872216817, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872217108, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_5A158695308CA418.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872217309, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872217560, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872217757, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872217880, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_355EDFCE7F3816F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872218064, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872218155, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_061233166775A3D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872218357, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872218536, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_AEBC23A8E4FA79A7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872218762, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872218916, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D7B1BF987A36C198.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872219183, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872219302, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AEA8660476D6039F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872219428, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872219559, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B15AC01DD6F6A05B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872219754, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872219866, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872220079, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872220165, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8D21704F39F1057D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872220254, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872220404, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5A2D3B4655B3286F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872220585, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872220644, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0E99BC6E1964CACE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872220711, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872220804, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872220913, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_32CA5EEFEC612C27.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872221060, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872221131, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A9C2D5B3A007F854.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872221316, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872221426, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_8895869DB6F5BBF5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872221505, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872221678, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872221813, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872221962, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872222216, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872222314, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872222456, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872222654, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872222787, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_11476E2D9783C34A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872222998, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872223147, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872223284, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872223519, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872223653, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872223760, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872223922, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224031, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224133, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224202, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224301, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224447, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224578, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224687, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224809, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224901, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872224983, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225079, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225133, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225206, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225269, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225332, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225394, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225466, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225566, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225620, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225705, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225777, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225899, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872225998, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872226054, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872226112, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872226180, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872226314, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872227131, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872227820, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872229217, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872230099, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872231105, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872232103, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872233070, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872233956, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872234952, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872235964, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872237052, "dur": 2331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872239383, "dur": 1863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872241246, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872242587, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872243447, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872244827, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872246478, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872247152, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872248008, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872248816, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872249534, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872250258, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872250993, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872251707, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872252444, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872253154, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872254553, "dur": 3089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872257643, "dur": 3351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872260995, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872261843, "dur": 1868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872263711, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872264454, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872264770, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872265128, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872266073, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872266479, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872267341, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872267481, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872267722, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872269852, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872270065, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872270224, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872270317, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872271838, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872272042, "dur": 1416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872273459, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872273598, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872273766, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872274910, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872275033, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872275449, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872275841, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872275917, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872278149, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872278257, "dur": 1974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872280231, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872280363, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872280479, "dur": 1553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872282032, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872282185, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872282823, "dur": 4097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872286922, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860872287048, "dur": 1676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872288789, "dur": 166316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872455107, "dur": 6644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872461753, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872461852, "dur": 1243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872463098, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872463607, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872465966, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872466062, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872468466, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872468562, "dur": 2425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872470988, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872471303, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872472500, "dur": 775, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872473285, "dur": 20216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872493503, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872493618, "dur": 22810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872516429, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872516588, "dur": 4395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750860872520984, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872521232, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872521493, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872521683, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872521878, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750860872521933, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872522173, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872522383, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872522556, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750860872522653, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750860872522715, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872522949, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523008, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750860872523070, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523141, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523201, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523355, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523517, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523599, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750860872523662, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523786, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872523877, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750860872523973, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750860872524069, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750860872524237, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872524431, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872524813, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860872524920, "dur": 209942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872202318, "dur": 14036, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872216390, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C5C1DFA646678D5B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872216458, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_5BD5A8CCC51B35E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872216569, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872216678, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_E7D994A1187DB9B1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872216838, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872217125, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9358C75F1190FC64.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872217334, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872217554, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2A0E14B20F8987CC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872217708, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872217849, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8192A56F15586443.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872218049, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_B519677B2E750245.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872218334, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872218446, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_1D0F8F342DF5CF0F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872218663, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872218769, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872219007, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872219192, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9179AE1968B95645.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872219352, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872219494, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_36001A2C42EABFDD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872219654, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872219801, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_ED9F64302AB7CA36.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872219969, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872220089, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_C1F8E474D4FC94AE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872220225, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_97D4D89498459919.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872220412, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872220576, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8F6AF5EA65F36C03.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872220626, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872220694, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872220776, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C166689E8CA2A04E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872220890, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872220989, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_395D75F92AC91BB0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872221133, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872221227, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_B3E6C0B422B85093.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872221381, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872221497, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872221603, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872221717, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872221840, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872221971, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872222109, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872222269, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872222340, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872222524, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872222744, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872222889, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872223103, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872223238, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872223371, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872223540, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872223693, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872223823, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872223964, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224103, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224185, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224256, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224395, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224564, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224668, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224772, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872224881, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225017, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225095, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225157, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225228, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225292, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225363, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225431, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225508, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225598, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225679, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225749, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225848, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872225974, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872226031, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872226098, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872226249, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872227109, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872227799, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872229169, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872230044, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872231047, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872232030, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872232988, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872233905, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872234878, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872235890, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872236990, "dur": 2282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872239273, "dur": 1862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872241135, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872242544, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872243420, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872244825, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872244942, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872246516, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872247172, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872248063, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872248882, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872249583, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872250318, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872251055, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872251763, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872252500, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872253226, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872254788, "dur": 2933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872257721, "dur": 3401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872261123, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872261636, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872263536, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872264100, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872264660, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872264751, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872265126, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872266068, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872266695, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872267643, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872267963, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872268065, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872268516, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872268803, "dur": 1413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872270218, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872270466, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872270552, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872270621, "dur": 2587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872273208, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872273469, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872273872, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872275286, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872275551, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll_B2C8007ACBA256CB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872275688, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872275751, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872275867, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872276312, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872277035, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872277155, "dur": 1615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872278771, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872278848, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872279169, "dur": 1368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872280537, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872280645, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.Shared.ref.dll_0A54E06D0D53005C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872280715, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872280994, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872281318, "dur": 1525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872282843, "dur": 4952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872287797, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872289859, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872290459, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860872290545, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872290936, "dur": 311275, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872603089, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872604996, "dur": 259, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872730783, "dur": 492, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860872605409, "dur": 125874, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750860872734496, "dur": 298, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750860872734799, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872202325, "dur": 14067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872216482, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872216598, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_8B7F52F4EF803EEA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872216772, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872216864, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_E5822FB88B5F8525.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872217175, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872217337, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_DB3537FAE77D4E90.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872217630, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872217758, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_97266B4C96017FCE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872217931, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872218061, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FD99BC8C7952C75B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872218233, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872218410, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_58154A82C8AEEED2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872218597, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872218749, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2598F284265933B9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872218956, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872219159, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_12CDA413F762C33F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872219317, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872219424, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_DB4D8D23A5102F12.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872219618, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872219764, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_15FE89EB11B2B491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872219942, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872220071, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A7772F47C16CAC56.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872220198, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872220310, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_60DE2D62F663D764.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872220439, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872220584, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872220657, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872220755, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872220871, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872220929, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_BA6BF233C73D6014.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872221088, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872221160, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_03EB462F708254BF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872221304, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872221375, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_665A96337EB7495A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872221499, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872221611, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872221744, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872221858, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872222031, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872222160, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872222272, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872222326, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872222479, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872222702, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872222830, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872223053, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872223214, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872223337, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872223411, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872223579, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872223706, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872223851, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224012, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224116, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224191, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224268, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224415, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224501, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224590, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224710, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224834, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224909, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872224987, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225084, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225145, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225214, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225272, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225337, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225398, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225485, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225560, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225661, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225730, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225822, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872225917, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872226009, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872226123, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872226231, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872227125, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872227812, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872229325, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872230181, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872231182, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872232131, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872233111, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872234004, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872235019, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872235997, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872237154, "dur": 2249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872239404, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872241290, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872242606, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872243524, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872245022, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872246561, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872247237, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872248100, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872248912, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872249634, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872250363, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872251105, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872251812, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872252551, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872253258, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872254846, "dur": 3008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872257854, "dur": 3354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872261208, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872262548, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872264351, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872264664, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872264749, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872265125, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872265932, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872266011, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872268384, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872268548, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872268665, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872268819, "dur": 3191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872272010, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872272150, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872274205, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872274304, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_91802D938EA41213.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872274442, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872274597, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872274860, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872275249, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872275306, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872276825, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872277521, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872277611, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872279529, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872279956, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872280337, "dur": 1271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872281658, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872282767, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860872282984, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872283411, "dur": 169462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872452874, "dur": 7792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872460668, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872460978, "dur": 4114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872465093, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872465178, "dur": 21482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872486660, "dur": 2416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872489096, "dur": 26240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872515338, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872515454, "dur": 3752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750860872519258, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872519368, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872519456, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872519759, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872519950, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872520012, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872520162, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872520457, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872520633, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872521073, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750860872521126, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872521347, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872521470, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872521599, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872521746, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750860872521804, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872521983, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750860872522044, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872522166, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872522296, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872522425, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750860872522495, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872522625, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872522758, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750860872522922, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872523155, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872523231, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750860872523283, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872523394, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750860872523523, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872523734, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872523832, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750860872523883, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872524032, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872524095, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750860872524160, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872524260, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872524313, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750860872524477, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860872525127, "dur": 209720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872202331, "dur": 14090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872216444, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872216517, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FDF1DF7383BE0CAD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872216710, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872216806, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_46E58BD6F1D896EF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872217122, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872217246, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872217517, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872217650, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_CA2F35FCE4A30BF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872217790, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872217935, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_161CFFFA16B8213C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872218085, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872218140, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872218410, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872218524, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872218592, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2D77F886ADC51F6A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872218788, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872218925, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA0E1C7BC0C607E9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872219206, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872219341, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_06E5AC6350BD5612.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872219528, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872219631, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7436A235A0073526.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872219842, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872219974, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_EE513D35A608BF93.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872220142, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872220246, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_875874FE279274C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872220420, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872220573, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_293131879DA5DD14.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872220627, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872220704, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872220785, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872220915, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872221013, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_74A9378003EBEF66.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872221136, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872221300, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_02823EF6668D812C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872221389, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872221512, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872221627, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872221763, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872221869, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872221991, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872222143, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872222257, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872222319, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872222485, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872222730, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872222842, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872223099, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872223228, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872223365, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872223509, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872223623, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872223740, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872223889, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224057, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224143, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224215, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224388, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224562, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224640, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224749, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224880, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872224953, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225063, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225130, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225225, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225289, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225356, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225425, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225513, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225590, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225647, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225755, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225896, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872225996, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872226053, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872226654, "dur": 142, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750860872226797, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872227512, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872228758, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872229752, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872230664, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872231706, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872232607, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872233565, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872234470, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872235480, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872236603, "dur": 2063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872238694, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872240160, "dur": 1834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872241995, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872243123, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872244033, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872245831, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872246898, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872247629, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872248537, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872249265, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872249973, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872250703, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872251451, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872252141, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872252883, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872253878, "dur": 2549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872256428, "dur": 3352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872259780, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872261378, "dur": 1937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872263315, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872264555, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872264687, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872264746, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872265123, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872265897, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872266082, "dur": 4754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872270838, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872270961, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872271025, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872271151, "dur": 1780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872272931, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872273073, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872273667, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872273802, "dur": 1247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872275050, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872275167, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872275262, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872275476, "dur": 3104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872278582, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872278753, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860872279207, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872279320, "dur": 3364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872282860, "dur": 171969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872454833, "dur": 8985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872463819, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872464099, "dur": 5120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872469220, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872469369, "dur": 6061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872475431, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872475634, "dur": 1806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872477441, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872477648, "dur": 16665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872494313, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872494405, "dur": 3260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872497667, "dur": 4917, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872502611, "dur": 1546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872504158, "dur": 1737, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872505910, "dur": 4158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872510069, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872510360, "dur": 4459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872514819, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872514973, "dur": 3364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872518337, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860872518431, "dur": 6955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750860872525412, "dur": 209465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860872736670, "dur": 3672, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1750860871298898, "dur": 678418, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750860871299644, "dur": 103200, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750860871802096, "dur": 3840, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750860871805940, "dur": 171359, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750860871807366, "dur": 101677, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750860871982750, "dur": 995, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750860871982390, "dur": 1525, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750860870778790, "dur": 2361, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870781156, "dur": 375, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870781603, "dur": 82, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870787112, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750860870781691, "dur": 11997, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870793696, "dur": 33873, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870827569, "dur": 484, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870828058, "dur": 136, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870828253, "dur": 440, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750860870781633, "dur": 12070, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870793742, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_661E9919313836F1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870794190, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FDF1DF7383BE0CAD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870794329, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870794387, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_DB3537FAE77D4E90.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870794444, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870794535, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_97266B4C96017FCE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870794685, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870794814, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0D7C2BFD403ED278.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870794924, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870795002, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_AEBC23A8E4FA79A7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870795098, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870795183, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA0E1C7BC0C607E9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870795274, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870795335, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_06E5AC6350BD5612.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870795455, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870795515, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B15AC01DD6F6A05B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870795636, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870795705, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A6AABF0E6ADA0B25.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870795809, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870795873, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4574CE91C336EF7D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870796019, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_A79420259383E98A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870796114, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870796173, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870796240, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2CC8B0A3337F4D14.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870796311, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870796394, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_BA6BF233C73D6014.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870796505, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6C262D80C93BD4F0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870796564, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870796618, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_665A96337EB7495A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870796748, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870796813, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870796878, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870796993, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797059, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797232, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797300, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797362, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797463, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797535, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797620, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797677, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797757, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797852, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870797931, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798001, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798079, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798154, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798273, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798351, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798442, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798536, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798651, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798742, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798860, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750860870798916, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870798993, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799070, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799139, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799209, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799322, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799391, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799607, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799705, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799772, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870799922, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870800018, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870800114, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870800182, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870800890, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870801541, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870802126, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870802775, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870803457, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870804106, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870804765, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870805534, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870806390, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870807215, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870807966, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870808867, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870809629, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870810612, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870811363, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870812153, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870813022, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870813827, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870814609, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870815401, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870816160, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870816986, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870817843, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870818636, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870819417, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870820273, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870821049, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870821839, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870822521, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870822722, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870823390, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870823500, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870823783, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870823893, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870823953, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870824192, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870824541, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870824673, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750860870825981, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750860870826085, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870826664, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750860870826958, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860870781638, "dur": 12071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870793748, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E6099293577F65EB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870794203, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D7331C31AE624D95.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870794332, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870794401, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870794465, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870794566, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_161CFFFA16B8213C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870794681, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870794781, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FD99BC8C7952C75B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870794908, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870794981, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_6C9DA28BBAE3FB82.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870795104, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870795318, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0462768E64EABD0A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870795461, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870795528, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7436A235A0073526.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870795650, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_ED9F64302AB7CA36.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870795743, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870795809, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_C1F8E474D4FC94AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870795922, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870795981, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6799CBAE2582DA9C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870796082, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870796144, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_EB44D0B764B7C8DC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870796326, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870796492, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_03EB462F708254BF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870796643, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_8895869DB6F5BBF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870796821, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870796886, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870796999, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797073, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797189, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797291, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797410, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797500, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870797654, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797737, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797815, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870797891, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798016, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798108, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798183, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798242, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798305, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798390, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798484, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798575, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798781, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870798906, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799003, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799060, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799136, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799189, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799289, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799370, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799593, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799679, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799802, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870799925, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870800020, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870800120, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870800189, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870800901, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870801559, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870802144, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870802824, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870803485, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870804125, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870804772, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870805564, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870806423, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870807245, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870807981, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870808917, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870809681, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870810673, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870811444, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870812224, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870813103, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870813898, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870814691, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870815487, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870816244, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870817065, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870817911, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870818696, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870819474, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870820305, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870821068, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870821847, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870822546, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870823199, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870823949, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870824204, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870824542, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870824599, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870824664, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750860870824849, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750860870827223, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750860870827314, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750860870781644, "dur": 12069, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870793716, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_A93D6DF8E051997E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870794141, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_C4615A0A510AFA65.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870794312, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9358C75F1190FC64.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870794436, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870794758, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870794844, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_061233166775A3D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870794977, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870795040, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_845B1BE9B09512AE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870795174, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870795243, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_12CDA413F762C33F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870795319, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870795406, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_DB4D8D23A5102F12.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870795673, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870795738, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A3EA01C4850945EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870795866, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8D21704F39F1057D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870796001, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_06C0A05E69B9BD23.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870796110, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870796181, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870796249, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C166689E8CA2A04E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870796316, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870796488, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A9C2D5B3A007F854.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870796612, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870796712, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870796842, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870796987, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797068, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797161, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797263, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797346, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797471, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797524, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870797708, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797792, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797875, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797936, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870797996, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798086, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798155, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798274, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798382, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798512, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798591, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798692, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798828, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798891, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870798967, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799099, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799169, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799271, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799358, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799420, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799613, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799707, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799775, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799862, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870799933, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870800010, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870800122, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870800227, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870800950, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870801575, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870802170, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870802841, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870803505, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870804141, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870804794, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870805566, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870806400, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870807239, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870807988, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870808900, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870809654, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870810633, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870811393, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870812166, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870813035, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870813829, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870814616, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870815420, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870816190, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870817020, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870817862, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870818646, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870819421, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870820271, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870821055, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870821836, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870822534, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870823236, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870823942, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870824202, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870824616, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750860870824835, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870824893, "dur": 1341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750860870826234, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750860870826310, "dur": 1742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860870781661, "dur": 12067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870793729, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_5651B02FF685D981.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870794147, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_9EFE93A4B6B28B73.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870794271, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_5A158695308CA418.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870794341, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870794427, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_CA2F35FCE4A30BF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870794540, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870794645, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C9234425C371F41A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870794803, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870794868, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870794995, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870795090, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2598F284265933B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870795192, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870795254, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_34F8FBC3386DE99E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870795327, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870795417, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_36001A2C42EABFDD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870795526, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870795597, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_165FE63856186A10.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870795688, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870795762, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A7772F47C16CAC56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870795860, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA115901BA18BB2D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870796012, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A8529918C73873A7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870796159, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0E99BC6E1964CACE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870796286, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870796349, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870796462, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_74A9378003EBEF66.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870796525, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870796608, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_02823EF6668D812C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870796730, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870796812, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870796870, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870796975, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797041, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797156, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797299, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797412, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797505, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870797649, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797733, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797842, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797913, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870797982, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798057, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798131, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798257, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798340, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798424, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798525, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798616, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798720, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870798907, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799013, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799080, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799150, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799221, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799329, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799401, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799597, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799685, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799807, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799895, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870799967, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870800031, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870800134, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870800888, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870801539, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870802117, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870802779, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870803452, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870804087, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870804734, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870805513, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870806352, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870807193, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870807932, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870808836, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870809607, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870810590, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870811349, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870812124, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870813006, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870813798, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870814593, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870815378, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870816146, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870816978, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870817795, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870818588, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870819385, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870820226, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870821007, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870821797, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870822486, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870822676, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870823364, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870823781, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870823902, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870823957, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870824198, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870824621, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870825173, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870825266, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870825347, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870825706, "dur": 1320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750860870827026, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750860870827089, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750860870827196, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860870781668, "dur": 12064, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870793734, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C5C1DFA646678D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870794255, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E4ECE7E8BB35882.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870794328, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870794385, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_54E2C669D74DB25A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870794446, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870794537, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8192A56F15586443.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870794715, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870794829, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870794938, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870795012, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2D77F886ADC51F6A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870795091, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870795194, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_070DA0E44072E4AB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870795282, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870795343, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A07FB6BEB1E7333E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870795483, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870795549, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8D8F81C2AD007FF4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870795636, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870795700, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870795778, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870795904, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_875874FE279274C8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870796025, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_293131879DA5DD14.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870796169, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870796237, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_82FEB68469624F24.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870796309, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870796369, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_32CA5EEFEC612C27.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870796464, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870796520, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_B3E6C0B422B85093.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870796597, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870796664, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870796828, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870796899, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870796960, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797039, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797129, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797247, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797324, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797449, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797517, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870797667, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797724, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797803, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797900, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870797972, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798049, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798107, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798194, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798254, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798317, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798414, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798516, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798597, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798711, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870798937, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799036, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799100, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799160, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799255, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799340, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799422, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799573, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799642, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799722, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799852, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799914, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870799998, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870800084, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870800163, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870800895, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870801571, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870802153, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870802815, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870803479, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870804120, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870804768, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870805542, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870806391, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870807221, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870807956, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870808862, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870809633, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870810618, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870811376, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870812146, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870813013, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870813811, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870814603, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870815392, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870816154, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870816982, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870817799, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870818601, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870819411, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870820259, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870821038, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870821827, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870822526, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870822816, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870823474, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870823602, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870823878, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870823943, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870824203, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870824613, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870824870, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860870825361, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870825450, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750860870825543, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860870826327, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750860870826445, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750860870827531, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870781674, "dur": 12063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870793738, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A137065F34CC9C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870794421, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FAD28CEFAD4EF7CD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870794669, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870794725, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4475AF2AFFABA926.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870794842, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870794931, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_58154A82C8AEEED2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870795033, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870795109, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870795235, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870795293, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9179AE1968B95645.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870795379, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870795521, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FF3C62C209E1F2AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870795641, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870795729, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DBE3A542E9C167DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870795813, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870795919, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_60DE2D62F663D764.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870795992, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870796079, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8F6AF5EA65F36C03.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870796148, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870796261, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_3FB038AB94CBD0EF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870796359, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870796448, "dur": 3962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870800443, "dur": 9566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860870810009, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870810123, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870810176, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870810379, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870811172, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870811948, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870812852, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870813651, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870814432, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870815235, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870815992, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870816823, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870817596, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870818413, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870819211, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870820041, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870820857, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870821618, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870822330, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870822827, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870823486, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870823767, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870823900, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870823950, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870824205, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870824604, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870825034, "dur": 1688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860870826722, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750860870826864, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750860870826967, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750860870827882, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870781680, "dur": 12089, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870793769, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9FEDC77DF18D321C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870794156, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0254B4EAD9FA5CB9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870794338, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_479F21D64DDF5A5C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870794469, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_79266F7FBDBF49B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870794570, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870794671, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_110B7A9752F5AD38.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870794826, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870794889, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_C268CA3985788B50.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870795050, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5A04CF1DC9DD1390.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870795170, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870795226, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C30E9AD099929D47.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870795295, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870795370, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A49295611C727860.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870795503, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870795565, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_15FE89EB11B2B491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870795653, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870795720, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_EE513D35A608BF93.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870795800, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870795870, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_97D4D89498459919.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870795972, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870796050, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D0B6A15278A42C4B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870796128, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870796268, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870796348, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870796425, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_ED2AB5385077AD72.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870796491, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870796557, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_857F5E2A119BA0C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870796682, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870796825, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870796892, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870796949, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797008, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797107, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797212, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797294, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797359, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797492, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_11476E2D9783C34A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870797552, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797638, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797704, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797818, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870797886, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798023, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798095, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798180, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798299, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798377, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798466, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798549, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798679, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798815, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798909, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870798992, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799107, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799183, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799314, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799380, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799585, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799667, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799754, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799865, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870799943, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870800013, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870800121, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870800883, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870801536, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870802115, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870802784, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870803367, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870804018, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870804653, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870805425, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870806233, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870807071, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870807794, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870808686, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870809483, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870810445, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870811201, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870811971, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870812848, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870812907, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870813697, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870814502, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870815298, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870816064, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870816887, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870817692, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870818479, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870819281, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870820106, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870820892, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870821663, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870822377, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870822721, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870823383, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870823689, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870823941, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870824184, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870824619, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870824968, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860870826048, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870826182, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750860870826584, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870826692, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750860870827340, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750860870827459, "dur": 95, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870781687, "dur": 12058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870793745, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5CDB3D0CA3CB09B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870794253, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_E5822FB88B5F8525.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870794330, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870794397, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2A0E14B20F8987CC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870794462, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870794562, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_355EDFCE7F3816F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870794654, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870794739, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_B519677B2E750245.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870794862, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870794948, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_1D0F8F342DF5CF0F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870795039, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870795132, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D7B1BF987A36C198.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870795301, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AEA8660476D6039F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870795435, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870795497, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_462079EBF000A463.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870795610, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870795672, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_65DFED6065744ECD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870795758, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870795836, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870795990, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5A2D3B4655B3286F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870796136, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_0897B8A47BC70881.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870796252, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870796311, "dur": 3912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870800273, "dur": 11849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860870812123, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870812226, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870812291, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870812380, "dur": 2461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870814894, "dur": 9237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860870824179, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870824251, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860870824540, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870824641, "dur": 1696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860870826337, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750860870826482, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750860870826601, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750860870827778, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750860870834067, "dur": 422, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 75155, "tid": 691, "ts": 1750860872743354, "dur": 13, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 75155, "tid": 691, "ts": 1750860872743961, "dur": 12, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 75155, "tid": 691, "ts": 1750860872744344, "dur": 7, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 75155, "tid": 691, "ts": 1750860872743398, "dur": 562, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872744158, "dur": 185, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872744382, "dur": 353, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 75155, "tid": 691, "ts": 1750860872741021, "dur": 3740, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}